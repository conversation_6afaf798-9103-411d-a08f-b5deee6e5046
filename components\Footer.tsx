'use client';

export default function Footer() {
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gray-900 dark:bg-black text-white py-16 transition-colors duration-300">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          <div className="col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">∞</span>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                InfiniFlow™
              </span>
            </div>
            <p className="text-gray-400 text-lg leading-relaxed mb-6">
              革命性的智慧自動化引擎，連接頂尖 AI 技術與您的商業工具，將複雜的營運流程自動化，為您的事業帶來無限可能。
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                <i className="ri-facebook-fill"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-blue-400 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                <i className="ri-twitter-fill"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-blue-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                <i className="ri-linkedin-fill"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                <i className="ri-youtube-fill"></i>
              </a>
            </div>
          </div>

          <div>
            <h4 className="text-xl font-semibold mb-6">快速導航</h4>
            <ul className="space-y-3">
              <li>
                <button 
                  onClick={() => scrollToSection('solutions')}
                  className="text-gray-400 hover:text-white transition-colors cursor-pointer"
                >
                  解決方案
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('industries')}
                  className="text-gray-400 hover:text-white transition-colors cursor-pointer"
                >
                  應用場景
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('why-us')}
                  className="text-gray-400 hover:text-white transition-colors cursor-pointer"
                >
                  為什麼選擇我們
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('cta')}
                  className="text-gray-400 hover:text-white transition-colors cursor-pointer"
                >
                  立即開始
                </button>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-xl font-semibold mb-6">聯絡資訊</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <i className="ri-phone-line text-blue-400"></i>
                <span className="text-gray-400">+886-2-1234-5678</span>
              </div>
              <div className="flex items-center space-x-3">
                <i className="ri-mail-line text-blue-400"></i>
                <span className="text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <i className="ri-map-pin-line text-blue-400"></i>
                <span className="text-gray-400">台北市信義區信義路五段7號</span>
              </div>
              <div className="flex items-center space-x-3">
                <i className="ri-time-line text-blue-400"></i>
                <span className="text-gray-400">週一至週五 9:00-18:00</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 md:mb-0">
              © 2024 InfiniFlow™. All rights reserved. Powered by AI Innovation.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">隱私政策</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">服務條款</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">Cookie 政策</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}