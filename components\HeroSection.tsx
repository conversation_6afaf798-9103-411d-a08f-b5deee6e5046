'use client';

import { useEffect, useRef } from 'react';

export default function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(() => {
        // 如果自動播放失敗，可以處理錯誤
      });
    }
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 背景影片效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-black">
        <div className="absolute inset-0 bg-black/30"></div>
        {/* 動態光流效果 */}
        <div className="absolute inset-0">
          <div className="infinity-flow"></div>
        </div>
      </div>

      {/* 內容 */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        <div className="mb-8">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-md px-4 py-2 rounded-full mb-6">
            <span className="text-3xl">∞</span>
            <span className="text-white font-semibold">InfiniFlow™ 智慧自動化引擎</span>
          </div>
        </div>
        
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          讓智慧加入您的團隊
        </h1>
        
        <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-2xl mx-auto">
          為您帶來最大的效益無限賦能
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button 
            onClick={() => scrollToSection('cta')}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 whitespace-nowrap"
          >
            立即開始您的數位轉型
          </button>
          <button 
            onClick={() => scrollToSection('solutions')}
            className="border-2 border-white/30 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:scale-105 whitespace-nowrap"
          >
            了解更多
          </button>
        </div>
      </div>

      {/* 滾動指示器 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <button 
          onClick={() => scrollToSection('solutions')}
          className="text-white/70 hover:text-white transition-colors cursor-pointer"
        >
          <i className="ri-arrow-down-line text-2xl"></i>
        </button>
      </div>

      <style jsx>{`
        .infinity-flow {
          background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
          background-size: 200% 200%;
          animation: infinityFlow 8s ease-in-out infinite;
        }
        
        .infinity-flow::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(ellipse at center, rgba(147, 51, 234, 0.2) 0%, transparent 70%);
          animation: pulse 4s ease-in-out infinite alternate;
        }
        
        @keyframes infinityFlow {
          0%, 100% { background-position: 0% 0%; }
          50% { background-position: 100% 100%; }
        }
        
        @keyframes pulse {
          0% { opacity: 0.3; }
          100% { opacity: 0.8; }
        }
      `}</style>
    </section>
  );
}