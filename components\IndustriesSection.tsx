'use client';

import { useState } from 'react';

export default function IndustriesSection() {
  const [activeTab, setActiveTab] = useState('ecommerce');

  const industries = {
    ecommerce: {
      title: '電商與零售業',
      subtitle: '智慧驅動，迅現倍增',
      icon: 'ri-shopping-cart-line',
      image: 'https://readdy.ai/api/search-image?query=Modern%20e-commerce%20automation%20dashboard%20with%20AI%20analytics%2C%20clean%20white%20background%2C%20professional%20business%20interface%20showing%20sales%20data%2C%20inventory%20management%2C%20and%20customer%20insights%20in%20a%20minimalist%20style&width=600&height=400&seq=ecommerce1&orientation=landscape',
      features: [
        '智慧庫存管理與預測',
        '個人化推薦引擎',
        '自動化客服與訂單處理',
        '動態定價策略優化'
      ]
    },
    realestate: {
      title: '房地產與中介業',
      subtitle: '專業呈現，加速成交',
      icon: 'ri-building-line',
      image: 'https://readdy.ai/api/search-image?query=Professional%20real%20estate%20automation%20platform%20interface%2C%20modern%20property%20management%20dashboard%20with%20virtual%20tours%2C%20client%20management%2C%20and%20deal%20tracking%20features%2C%20clean%20white%20background%2C%20sophisticated%20business%20design&width=600&height=400&seq=realestate1&orientation=landscape',
      features: [
        '智慧物件配對與推薦',
        '自動化市場分析報告',
        '虛擬看房體驗優化',
        '客戶關係自動化管理'
      ]
    },
    education: {
      title: '教育、顧問與知識付費',
      subtitle: '知識變現，無費力',
      icon: 'ri-book-open-line',
      image: 'https://readdy.ai/api/search-image?query=Educational%20technology%20platform%20with%20AI-powered%20learning%20management%20system%2C%20course%20creation%20tools%2C%20student%20analytics%2C%20and%20knowledge%20monetization%20features%2C%20clean%20modern%20interface%20with%20white%20background&width=600&height=400&seq=education1&orientation=landscape',
      features: [
        '智慧課程內容生成',
        '個人化學習路徑規劃',
        '自動化學員互動與追蹤',
        '知識產品銷售優化'
      ]
    },
    saas: {
      title: 'SaaS 與軟體服務業',
      subtitle: '資訊同步，價值加速',
      icon: 'ri-cloud-line',
      image: 'https://readdy.ai/api/search-image?query=SaaS%20platform%20automation%20dashboard%20with%20cloud%20services%2C%20API%20integrations%2C%20user%20management%2C%20and%20analytics%20tools%2C%20modern%20tech%20interface%20with%20clean%20white%20background%2C%20professional%20software%20design&width=600&height=400&seq=saas1&orientation=landscape',
      features: [
        '用戶onboarding自動化',
        '產品使用數據分析',
        '智慧客戶成功管理',
        '訂閱模式優化策略'
      ]
    },
    beauty: {
      title: '醫美與美業',
      subtitle: '創造渴望，加速服務',
      icon: 'ri-heart-pulse-line',
      image: 'https://readdy.ai/api/search-image?query=Beauty%20and%20medical%20aesthetics%20business%20automation%20platform%2C%20appointment%20scheduling%2C%20client%20management%2C%20treatment%20tracking%2C%20and%20marketing%20automation%20tools%2C%20elegant%20clean%20interface%20with%20white%20background&width=600&height=400&seq=beauty1&orientation=landscape',
      features: [
        '智慧預約排程管理',
        '個人化美容方案推薦',
        '客戶滿意度追蹤分析',
        '行銷活動自動化執行'
      ]
    },
    content: {
      title: '內容創作者經濟',
      subtitle: '內容放大，專注創作',
      icon: 'ri-palette-line',
      image: 'https://readdy.ai/api/search-image?query=Content%20creator%20economy%20platform%20with%20AI%20content%20generation%20tools%2C%20social%20media%20automation%2C%20audience%20analytics%2C%20and%20monetization%20features%2C%20creative%20modern%20interface%20with%20clean%20white%20background&width=600&height=400&seq=content1&orientation=landscape',
      features: [
        '多平台內容自動發布',
        '受眾分析與互動優化',
        '創作靈感智慧生成',
        '收益模式自動化管理'
      ]
    }
  };

  const currentIndustry = industries[activeTab as keyof typeof industries];

  return (
    <section id="industries" className="py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            您的產業，如何指數成長？
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            探索 InfiniFlow™ 如何為不同產業帶來革命性的自動化轉型
          </p>
        </div>

        {/* 產業選擇標籤 */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {Object.entries(industries).map(([key, industry]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 whitespace-nowrap ${
                activeTab === key
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              <i className={`${industry.icon} mr-2`}></i>
              {industry.title}
            </button>
          ))}
        </div>

        {/* 產業內容展示 */}
        <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden">
          <div className="grid lg:grid-cols-2 gap-0">
            <div className="p-12">
              <div className="mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                  <i className={`${currentIndustry.icon} text-white text-3xl`}></i>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  {currentIndustry.title}
                </h3>
                <p className="text-xl text-blue-600 dark:text-blue-400 font-semibold mb-6">
                  {currentIndustry.subtitle}
                </p>
              </div>

              <div className="space-y-4">
                {currentIndustry.features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-4 group">
                    <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mt-1 group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-check-line text-white text-sm"></i>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 text-lg">{feature}</p>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg whitespace-nowrap">
                  了解 {currentIndustry.title} 解決方案
                </button>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-12 flex items-center justify-center">
              <div className="relative group">
                <img
                  src={currentIndustry.image}
                  alt={currentIndustry.title}
                  className="w-full h-80 object-cover rounded-2xl shadow-xl group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}