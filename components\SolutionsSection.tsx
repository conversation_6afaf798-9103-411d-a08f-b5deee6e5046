'use client';

export default function SolutionsSection() {
  return (
    <section id="solutions" className="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            隆重介紹我們的解決方案
          </h2>
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-semibold text-blue-600 dark:text-blue-400 mb-6">
              InfiniFlow™：不只是一個工具，也是您的 7×24 小時數位成長團隊
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              InfiniFlow™ 是一個革命性的工作流程引擎。它連接了頂尖的人工智慧晶片（如 Gemini）、內容生產平台（如 Builder.io）與您日常使用的商業工具，將複雜的營運流程自動化。
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="group bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-robot-line text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">AI 智慧串接</h4>
            <p className="text-gray-600 dark:text-gray-300">
              整合頂尖 AI 技術，包括 Gemini、GPT 等，為您的業務流程注入智慧決策能力。
            </p>
          </div>

          <div className="group bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-flow-chart text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">工作流程自動化</h4>
            <p className="text-gray-600 dark:text-gray-300">
              將重複性工作完全自動化，讓您的團隊專注於高價值的創意和策略工作。
            </p>
          </div>

          <div className="group bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-links-line text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">無縫整合</h4>
            <p className="text-gray-600 dark:text-gray-300">
              與您現有的商業工具完美整合，無需改變既有的工作習慣，立即提升效率。
            </p>
          </div>

          <div className="group bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-orange-600 to-red-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-time-line text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">7×24 小時運作</h4>
            <p className="text-gray-600 dark:text-gray-300">
              全天候不間斷運作，確保您的業務流程永不停歇，競爭優勢持續放大。
            </p>
          </div>

          <div className="group bg-gradient-to-br from-teal-50 to-cyan-100 dark:from-teal-900/20 dark:to-cyan-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-cyan-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-settings-3-line text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">深度客製化</h4>
            <p className="text-gray-600 dark:text-gray-300">
              根據您的獨特需求量身打造，不是套用模板，而是專屬的解決方案。
            </p>
          </div>

          <div className="group bg-gradient-to-br from-violet-50 to-purple-100 dark:from-violet-900/20 dark:to-purple-900/20 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer">
            <div className="w-16 h-16 bg-gradient-to-r from-violet-600 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-line-chart-line text-white text-2xl"></i>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">持續優化</h4>
            <p className="text-gray-600 dark:text-gray-300">
              隨著市場變化和 AI 進化，我們持續為您的系統進行迭代升級。
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}