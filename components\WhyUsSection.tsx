'use client';

export default function WhyUsSection() {
  const features = [
    {
      icon: 'ri-puzzle-line',
      title: '深度客製化',
      description: '我們不套用模板，而是深入理解您的業務流程，為您打造專屬的 InfiniFlow™。',
      color: 'from-blue-600 to-indigo-600'
    },
    {
      icon: 'ri-chat-3-line',
      title: '專家級提示工程',
      description: '我們知道如何與 AI 對話，確保其產生的內容精準、合適，符合您的品牌調性。',
      color: 'from-purple-600 to-pink-600'
    },
    {
      icon: 'ri-plug-line',
      title: '無縫整合',
      description: '我們負責所有複雜的技術串接，您只需享受自動化帶來的成果。',
      color: 'from-green-600 to-emerald-600'
    },
    {
      icon: 'ri-refresh-line',
      title: '持續優化',
      description: '市場在變，AI 在進化。我們將持續為您的 InfiniFlow™ 進行迭代，確保您永遠領先一步。',
      color: 'from-orange-600 to-red-600'
    }
  ];

  return (
    <section id="why-us" className="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            為什麼選擇我們？
          </h2>
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-semibold text-blue-600 dark:text-blue-400 mb-6">
              我們不僅僅是交付工具，我們也是您的 AI 商業架構師
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              市場有許多工具，但只有策略才能將它們化為真正的競爭優勢。
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-8 rounded-2xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer"
            >
              <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <i className={`${feature.icon} text-white text-2xl`}></i>
              </div>
              <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {feature.title}
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* 成功案例數據 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">數據證明我們的實力</h3>
            <p className="text-blue-100 text-lg">真實客戶的成功見證</p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">95%</div>
              <div className="text-blue-100">效率提升</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">60%</div>
              <div className="text-blue-100">成本節省</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">300+</div>
              <div className="text-blue-100">成功案例</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">99.9%</div>
              <div className="text-blue-100">系統穩定性</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}